import { useState, useEffect, useRef, useCallback } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { Input } from "@/components/ui/input";
import { MapPin } from "lucide-react";

// A simple debounce utility
const debounce = <F extends (...args: any[]) => any>(func: F, delay: number) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return (...args: Parameters<F>): void => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Singleton Google Maps loader to prevent multiple initializations
let googleMapsPromise: Promise<void> | null = null;

const initializeGoogleMaps = async (): Promise<void> => {
  if (googleMapsPromise) {
    return googleMapsPromise;
  }

  const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
  if (!apiKey || apiKey === "your_google_places_api_key_here") {
    throw new Error("Google Places API key not configured");
  }

  googleMapsPromise = (async () => {
    // Check if Google Maps is already loaded
    if ((window as any).google?.maps?.places) {
      return;
    }

    const loader = new Loader({
      apiKey,
      version: "weekly",
      libraries: ["places"]
    });

    await loader.importLibrary("places");
  })();

  return googleMapsPromise;
};

// Type declarations for Google Maps
declare global {
  namespace google {
    namespace maps {
      namespace places {
        interface AutocompleteSuggestion {
          placePrediction?: {
            place: string;
            placeId: string;
            text: {
              text: string;
            };
            structuredFormat: {
              mainText: {
                text: string;
              };
              secondaryText: {
                text: string;
              };
            };
          };
        }

        interface Place {
          new (options: { id: string }): Place;
          fetchFields(options: { fields: string[] }): Promise<{ place: Place }>;
          formattedAddress?: string;
          displayName?: string;
        }

        interface AutocompleteSessionToken {}

        enum PlacesServiceStatus {
          OK = 'OK'
        }
      }
    }
  }
}

interface GooglePlacesAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

export default function GooglePlacesAutocomplete({
  value,
  onChange,
  placeholder = "Enter your location",
  className = "",
  required = false,
}: GooglePlacesAutocompleteProps) {
  const [predictions, setPredictions] = useState<google.maps.places.AutocompleteSuggestion[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const sessionToken = useRef<google.maps.places.AutocompleteSessionToken | undefined>(undefined);

  // 1. Initialize the Google Maps Services (runs only once)
  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeGoogleMaps();
        sessionToken.current = new (window as any).google.maps.places.AutocompleteSessionToken();
        setIsLoaded(true);
      } catch (err) {
        console.error("Error loading Google Places API:", err);
        setError(err instanceof Error ? err.message : "Failed to load Google Places API");
      }
    };

    initialize();
  }, []);

  // 2. Fetch predictions when the input value changes (with debouncing)
  const fetchPredictions = useCallback(
    debounce(async (inputValue: string) => {
      if (!inputValue.trim() || !isLoaded) {
        setPredictions([]);
        return;
      }

      try {
        // Use the new AutocompleteSuggestion API
        const request = {
          input: inputValue,
          includedPrimaryTypes: ['locality'], // equivalent to (cities)
          sessionToken: sessionToken.current,
        };

        const { suggestions } = await (window as any).google.maps.places.AutocompleteSuggestion.fetchAutocompleteSuggestions(request);

        if (suggestions) {
          setPredictions(suggestions);
        } else {
          setPredictions([]);
        }
      } catch (error) {
        console.error('Error fetching predictions:', error);
        setPredictions([]);
      }
    }, 300), // 300ms debounce delay
    [isLoaded]
  );

  useEffect(() => {
    if (!error && isLoaded) {
      fetchPredictions(value);
    }
  }, [value, fetchPredictions, error, isLoaded]);

  // 3. Handle when a user clicks on a prediction
  const handlePredictionClick = async (prediction: google.maps.places.AutocompleteSuggestion) => {
    if (!prediction.placePrediction?.placeId) return;

    try {
      // Use the new Place API to get place details
      const place = new (window as any).google.maps.places.Place({
        id: prediction.placePrediction.placeId
      });

      const { place: placeResult } = await place.fetchFields({
        fields: ['formattedAddress']
      });

      if (placeResult?.formattedAddress) {
        onChange(placeResult.formattedAddress); // Update the parent form
        setPredictions([]); // Clear the predictions list
        // Create a new session token for the next series of calls
        sessionToken.current = new (window as any).google.maps.places.AutocompleteSessionToken();
      } else {
        // Fallback to prediction text
        onChange(prediction.placePrediction?.text?.text || '');
        setPredictions([]);
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
      // Fallback: use the prediction text if place details fail
      onChange(prediction.placePrediction?.text?.text || '');
      setPredictions([]);
    }
  };

  if (error) {
    // Fallback to regular input
    return (
      <div className="relative">
        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`pl-10 ${className}`}
          required={required}
        />
        <p className="text-xs text-amber-600 mt-1">
          Location autocomplete unavailable - please enter manually
        </p>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground z-10 pointer-events-none" />
      <Input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={`pl-10 ${className}`}
        autoComplete="off"
        required={required}
      />
      {predictions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-popover border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
          {predictions.map((prediction, index) => (
            <div
              key={prediction.placePrediction?.placeId || index}
              onClick={() => handlePredictionClick(prediction)}
              className="px-4 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground border-b border-border last:border-b-0"
            >
              {prediction.placePrediction?.text?.text || 'Unknown location'}
            </div>
          ))}
        </div>
      )}
      {!isLoaded && !error && (
        <p className="text-xs text-muted-foreground mt-1">
          Loading location autocomplete...
        </p>
      )}
    </div>
  );
}
