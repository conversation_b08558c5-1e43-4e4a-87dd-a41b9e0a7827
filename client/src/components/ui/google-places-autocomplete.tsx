import { useState, useEffect, useRef, useCallback } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { Input } from "@/components/ui/input";
import { MapPin } from "lucide-react";

// A simple debounce utility
const debounce = <F extends (...args: any[]) => any>(func: F, delay: number) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return (...args: Parameters<F>): void => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Type declarations for Google Maps
declare global {
  namespace google {
    namespace maps {
      namespace places {
        interface AutocompletePrediction {
          place_id?: string;
          description: string;
        }

        interface AutocompleteService {
          getPlacePredictions(
            request: any,
            callback: (predictions: AutocompletePrediction[] | null, status: any) => void
          ): void;
        }

        interface PlacesService {
          getDetails(
            request: any,
            callback: (place: any, status: any) => void
          ): void;
        }

        interface AutocompleteSessionToken {}

        enum PlacesServiceStatus {
          OK = 'OK'
        }
      }
    }
  }
}

interface GooglePlacesAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

export default function GooglePlacesAutocomplete({
  value,
  onChange,
  placeholder = "Enter your location",
  className = "",
  required = false,
}: GooglePlacesAutocompleteProps) {
  const [predictions, setPredictions] = useState<google.maps.places.AutocompletePrediction[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);
  const sessionToken = useRef<google.maps.places.AutocompleteSessionToken | undefined>(undefined);

  // 1. Initialize the Google Maps Services (runs only once)
  useEffect(() => {
    const initialize = async () => {
      const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
      if (!apiKey || apiKey === "your_google_places_api_key_here") {
        setError("Google Places API key not configured");
        return;
      }

      try {
        const loader = new Loader({ apiKey, version: "weekly", libraries: ["places"] });
        await loader.load();

        autocompleteService.current = new (window as any).google.maps.places.AutocompleteService();
        // We need an element to attach the PlacesService to, a hidden div works well.
        const hiddenDiv = document.createElement('div');
        placesService.current = new (window as any).google.maps.places.PlacesService(hiddenDiv);
        sessionToken.current = new (window as any).google.maps.places.AutocompleteSessionToken();

        setIsLoaded(true);
      } catch (err) {
        console.error("Error loading Google Places API:", err);
        setError("Failed to load Google Places API");
      }
    };

    initialize();
  }, []);

  // 2. Fetch predictions when the input value changes (with debouncing)
  const fetchPredictions = useCallback(
    debounce((inputValue: string) => {
      if (autocompleteService.current && inputValue.trim()) {
        autocompleteService.current.getPlacePredictions(
          {
            input: inputValue,
            types: ['(cities)'],
            sessionToken: sessionToken.current,
          },
          (preds, status) => {
            if (status === (window as any).google?.maps?.places?.PlacesServiceStatus?.OK && preds) {
              setPredictions(preds);
            } else {
              setPredictions([]);
            }
          }
        );
      } else {
        setPredictions([]);
      }
    }, 300), // 300ms debounce delay
    []
  );

  useEffect(() => {
    if (isLoaded) {
      fetchPredictions(value);
    }
  }, [value, fetchPredictions, isLoaded]);

  // 3. Handle when a user clicks on a prediction
  const handlePredictionClick = (prediction: google.maps.places.AutocompletePrediction) => {
    if (!placesService.current || !prediction.place_id) return;

    placesService.current.getDetails(
      {
        placeId: prediction.place_id,
        fields: ['formatted_address'], // Request only the fields you need
        sessionToken: sessionToken.current,
      },
      (place, status) => {
        if (status === (window as any).google?.maps?.places?.PlacesServiceStatus?.OK && place?.formatted_address) {
          onChange(place.formatted_address); // Update the parent form
          setPredictions([]); // Clear the predictions list
          // Create a new session token for the next series of calls
          sessionToken.current = new (window as any).google.maps.places.AutocompleteSessionToken();
        }
      }
    );
  };

  if (error) {
    // Fallback to regular input
    return (
      <div className="relative">
        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`pl-10 ${className}`}
          required={required}
        />
        <p className="text-xs text-amber-600 mt-1">
          Location autocomplete unavailable - please enter manually
        </p>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground z-10 pointer-events-none" />
      <Input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={`pl-10 ${className}`}
        autoComplete="off"
        required={required}
      />
      {predictions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-popover border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
          {predictions.map((prediction) => (
            <div
              key={prediction.place_id}
              onClick={() => handlePredictionClick(prediction)}
              className="px-4 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground border-b border-border last:border-b-0"
            >
              {prediction.description}
            </div>
          ))}
        </div>
      )}
      {!isLoaded && !error && (
        <p className="text-xs text-muted-foreground mt-1">
          Loading location autocomplete...
        </p>
      )}
    </div>
  );
}
