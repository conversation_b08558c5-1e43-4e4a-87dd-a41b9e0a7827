import { useEffect, useRef, useState } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { Input } from "@/components/ui/input";
import { MapPin } from "lucide-react";

// Type definitions for Google Maps (safe fallback)
type GoogleMapsAutocomplete = any;

interface GooglePlacesAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

export default function GooglePlacesAutocomplete({
  value,
  onChange,
  placeholder = "Enter your location",
  className = "",
  required = false,
}: GooglePlacesAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<GoogleMapsAutocomplete | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeAutocomplete = async () => {
      const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
      
      if (!apiKey || apiKey === 'your_google_places_api_key_here') {
        setError("Google Places API key not configured");
        return;
      }

      try {
        const loader = new Loader({
          apiKey,
          version: "weekly",
          libraries: ["places"],
        });

        await loader.importLibrary("places");

        if (inputRef.current && (window as any).google) {
          autocompleteRef.current = new (window as any).google.maps.places.Autocomplete(
            inputRef.current,
            {
              types: ["(cities)"],
              fields: ["formatted_address", "geometry", "name"],
            }
          );

          autocompleteRef.current.addListener("place_changed", () => {
            const place = autocompleteRef.current?.getPlace();
            if (place?.formatted_address) {
              onChange(place.formatted_address);
            }
          });
        }

        setIsLoaded(true);
      } catch (err) {
        console.error("Error loading Google Places API:", err);
        setError("Failed to load Google Places API");
      }
    };

    initializeAutocomplete();

    return () => {
      if (autocompleteRef.current && (window as any).google) {
        (window as any).google.maps.event.clearInstanceListeners(autocompleteRef.current);
      }
    };
  }, [onChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  if (error) {
    // Fallback to regular input if Google Places fails
    return (
      <div className="relative">
        <MapPin className="absolute left-3 top-3 w-4 h-4 text-neutral-400" />
        <Input
          value={value}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={`pl-10 ${className}`}
          required={required}
        />
        <p className="text-xs text-amber-600 mt-1">
          Location autocomplete unavailable - please enter manually
        </p>
      </div>
    );
  }

  return (
    <div className="relative">
      <MapPin className="absolute left-3 top-3 w-4 h-4 text-neutral-400" />
      <Input
        ref={inputRef}
        value={value}
        onChange={handleInputChange}
        placeholder={placeholder}
        className={`pl-10 ${className}`}
        required={required}
      />
      {!isLoaded && (
        <p className="text-xs text-neutral-500 mt-1">
          Loading location autocomplete...
        </p>
      )}
      {isLoaded && (
        <p className="text-xs text-neutral-500 mt-1">
          Start typing to search for your location
        </p>
      )}
    </div>
  );
}
