import { useEffect, useRef, useState, useCallback } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { Input } from "@/components/ui/input";
import { MapPin } from "lucide-react";

// Type declarations for Google Maps
declare global {
  namespace google {
    namespace maps {
      namespace places {
        interface Place {
          formattedAddress?: string;
          displayName?: string;
          location?: any;
        }

        interface PlacesLibrary {
          PlaceAutocompleteElement: new (options?: any) => HTMLElement;
        }
      }
    }
  }
}

interface PlaceSelectEvent extends CustomEvent {
  detail: {
    place: {
      formattedAddress?: string;
      displayName?: string;
      location?: any;
    };
  };
}

interface GooglePlacesAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

export default function GooglePlacesAutocomplete({
  value,
  onChange,
  placeholder = "Enter your location",
  className = "",
  required = false,
}: GooglePlacesAutocompleteProps) {
  const autocompleteRef = useRef<HTMLElement | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Use a ref to store the onChange handler to avoid it being a dependency
  const onChangeRef = useRef(onChange);
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // Memoize the event handler to ensure the reference is stable for cleanup
  const handlePlaceSelect = useCallback((event: Event) => {
    const place = (event as PlaceSelectEvent).detail.place;
    if (place?.formattedAddress) {
      onChangeRef.current(place.formattedAddress);
    }
  }, []);

  useEffect(() => {
    const initialize = async () => {
      const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
      if (!apiKey || apiKey === "your_google_places_api_key_here") {
        setError("Google Places API key not configured");
        return;
      }

      try {
        const loader = new Loader({ apiKey, version: "weekly" });
        // Use importLibrary to load the 'places' library
        const { PlaceAutocompleteElement } = (await loader.importLibrary(
          "places",
        )) as any;

        // Create the element and keep a reference to it
        const autocomplete = new PlaceAutocompleteElement({
          // Pass configuration in the constructor
          types: ["(cities)"],
        });

        autocomplete.addEventListener("gmp-placeselect", handlePlaceSelect);
        autocompleteRef.current = autocomplete;

        // Find the container and append the element
        const container = document.getElementById("autocomplete-container");
        if (container) {
          container.innerHTML = "";
          container.appendChild(autocomplete);
        }
      } catch (err) {
        console.error("Error loading Google Places API:", err);
        setError("Failed to load Google Places API");
      }
    };

    initialize();

    // Cleanup function
    return () => {
      if (autocompleteRef.current) {
        autocompleteRef.current.removeEventListener(
          "gmp-placeselect",
          handlePlaceSelect,
        );
      }
    };
  }, [handlePlaceSelect]); // Effect runs only once

  // Sync the external value with the autocomplete element's value
  useEffect(() => {
    if (autocompleteRef.current) {
      (autocompleteRef.current as any).value = value;
      (autocompleteRef.current as any).placeholder = placeholder;
    }
  }, [value, placeholder]);

  if (error) {
    // Fallback to regular input
    return (
      <div className="relative">
        <MapPin className="absolute left-3 top-3 w-4 h-4 text-neutral-400" />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`pl-10 ${className}`}
          required={required}
        />
        <p className="text-xs text-amber-600 mt-1">
          Location autocomplete unavailable - please enter manually.
        </p>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground z-10 pointer-events-none" />
      <div id="autocomplete-container" className="w-full" />
    </div>
  );
}
