import { useState, useEffect, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { MapPin } from "lucide-react";

// A simple debounce utility
const debounce = <F extends (...args: any[]) => any>(func: F, delay: number) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return (...args: Parameters<F>): void => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Types for Google Places API responses
interface PlacesPrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

interface PlacesAutocompleteResponse {
  predictions: PlacesPrediction[];
  status: string;
}

interface PlaceDetailsResponse {
  result: {
    formatted_address: string;
    name: string;
  };
  status: string;
}

interface GooglePlacesAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

export default function GooglePlacesAutocomplete({
  value,
  onChange,
  placeholder = "Enter your location",
  className = "",
  required = false,
}: GooglePlacesAutocompleteProps) {
  const [predictions, setPredictions] = useState<PlacesPrediction[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [sessionToken] = useState(() => crypto.randomUUID()); // Generate a session token

  // Check if API key is configured
  useEffect(() => {
    const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
    if (!apiKey || apiKey === "your_google_places_api_key_here") {
      setError("Google Places API key not configured");
    }
  }, []);

  // 2. Fetch predictions when the input value changes (with debouncing)
  const fetchPredictions = useCallback(
    debounce(async (inputValue: string) => {
      if (!inputValue.trim()) {
        setPredictions([]);
        return;
      }

      const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
      if (!apiKey || apiKey === "your_google_places_api_key_here") {
        return;
      }

      try {
        // Use your backend API endpoint to proxy the Places API request
        const response = await fetch('/api/places/autocomplete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            input: inputValue,
            types: '(cities)',
            sessionToken: sessionToken,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: PlacesAutocompleteResponse = await response.json();

        if (data.status === 'OK') {
          setPredictions(data.predictions);
        } else {
          console.warn('Places API error:', data.status);
          setPredictions([]);
        }
      } catch (error) {
        console.error('Error fetching predictions:', error);
        setPredictions([]);
      }
    }, 300), // 300ms debounce delay
    [sessionToken]
  );

  useEffect(() => {
    if (!error) {
      fetchPredictions(value);
    }
  }, [value, fetchPredictions, error]);

  // 3. Handle when a user clicks on a prediction
  const handlePredictionClick = async (prediction: PlacesPrediction) => {
    if (!prediction.place_id) return;

    const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
    if (!apiKey || apiKey === "your_google_places_api_key_here") {
      // Fallback to prediction description
      onChange(prediction.description);
      setPredictions([]);
      return;
    }

    try {
      // Use your backend API endpoint to get place details
      const response = await fetch('/api/places/details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          place_id: prediction.place_id,
          fields: 'formatted_address',
          sessionToken: sessionToken,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: PlaceDetailsResponse = await response.json();

      if (data.status === 'OK' && data.result?.formatted_address) {
        onChange(data.result.formatted_address); // Update the parent form
        setPredictions([]); // Clear the predictions list
      } else {
        // Fallback to prediction description
        onChange(prediction.description);
        setPredictions([]);
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
      // Fallback: use the prediction description if place details fail
      onChange(prediction.description);
      setPredictions([]);
    }
  };

  if (error) {
    // Fallback to regular input
    return (
      <div className="relative">
        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`pl-10 ${className}`}
          required={required}
        />
        <p className="text-xs text-amber-600 mt-1">
          Location autocomplete unavailable - please enter manually
        </p>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground z-10 pointer-events-none" />
      <Input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={`pl-10 ${className}`}
        autoComplete="off"
        required={required}
      />
      {predictions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-popover border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
          {predictions.map((prediction) => (
            <div
              key={prediction.place_id}
              onClick={() => handlePredictionClick(prediction)}
              className="px-4 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground border-b border-border last:border-b-0"
            >
              {prediction.description}
            </div>
          ))}
        </div>
      )}

    </div>
  );
}
