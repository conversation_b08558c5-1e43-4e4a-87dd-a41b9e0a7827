@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 210 11% 98%;
  --foreground: 20 14.3% 4.1%;
  --muted: 210 9% 96%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 210 11% 98%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 214 13% 90%;
  --input: 214 13% 90%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 158 64% 52%;
  --secondary-foreground: 0 0% 98%;
  --accent: 43 96% 56%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 207 90% 54%;
  --radius: 0.5rem;
  
  /* Additional custom properties for the design */
  --neutral-50: 210 11% 98%;
  --neutral-100: 214 13% 96%;
  --neutral-200: 213 13% 88%;
  --neutral-300: 212 13% 81%;
  --neutral-400: 213 9% 58%;
  --neutral-500: 215 8% 45%;
  --neutral-600: 215 14% 34%;
  --neutral-700: 216 19% 25%;
  --neutral-800: 217 25% 18%;
  --neutral-900: 222 25% 11%;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 158 64% 52%;
  --secondary-foreground: 0 0% 98%;
  --accent: 43 96% 56%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .bg-neutral-50 {
    background-color: hsl(var(--neutral-50));
  }
  
  .text-neutral-600 {
    color: hsl(var(--neutral-600));
  }
  
  .text-neutral-700 {
    color: hsl(var(--neutral-700));
  }
  
  .text-neutral-800 {
    color: hsl(var(--neutral-800));
  }
  
  .text-neutral-900 {
    color: hsl(var(--neutral-900));
  }
  
  .border-neutral-200 {
    border-color: hsl(var(--neutral-200));
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--neutral-400)) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--neutral-400));
    border-radius: 3px;
  }

  /* Google Places Autocomplete Styling */
  gmp-place-autocomplete {
    width: 100%;
    display: block;
  }

  gmp-place-autocomplete::part(input) {
    height: 40px;
    width: 100%;
    padding-left: 40px;
    padding-right: 12px;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    font-size: 14px;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    outline: none;
    box-shadow: none;
    font-family: inherit;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  gmp-place-autocomplete::part(input):focus {
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
  }

  gmp-place-autocomplete::part(input)::placeholder {
    color: hsl(var(--muted-foreground));
  }

  /* Style the dropdown */
  gmp-place-autocomplete::part(listbox) {
    background-color: hsl(var(--popover));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    margin-top: 4px;
    max-height: 200px;
    overflow-y: auto;
  }

  gmp-place-autocomplete::part(option) {
    padding: 8px 12px;
    color: hsl(var(--popover-foreground));
    cursor: pointer;
    border-bottom: 1px solid hsl(var(--border));
  }

  gmp-place-autocomplete::part(option):hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  gmp-place-autocomplete::part(option):last-child {
    border-bottom: none;
  }

  /* Dark mode adjustments */
  .dark gmp-place-autocomplete::part(input) {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    border-color: hsl(var(--input));
  }

  .dark gmp-place-autocomplete::part(listbox) {
    background-color: hsl(var(--popover));
    border-color: hsl(var(--border));
  }

  .dark gmp-place-autocomplete::part(option) {
    color: hsl(var(--popover-foreground));
  }
}
